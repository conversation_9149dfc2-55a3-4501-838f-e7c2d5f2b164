import { useSelector } from "react-redux";

export default function About() {
    const profile = useSelector((state: any) => state.user.user.profile);
  return (
    <div>
      <h1>About Us</h1>
      <p>Welcome to the About page!</p>
      {profile && (
        <div>
          <h2>{profile.name}</h2>
          <p>Email: {profile.email}</p>
          <p>Roles: {profile.roles.join(", ")}</p>
        </div>
      )}
    </div>
  );
}
