import { useSelector } from 'react-redux';
import { Navigate, Outlet } from 'react-router-dom'
import type { RootState } from 'src/store';
import type { Role } from 'src/types/user.type';

interface Props {
  allowedRoles?: Role[]; // Array of allowed roles
  children?: React.ReactNode; // Child components to render
  redirectPath?: string; // Customizable redirect path
}



export const ProtectedRoute: React.FC<Props> = ({
  allowedRoles = ["Admin", "User"], // Default to all roles
  children,
  redirectPath = "/",
}) => {
  const profile = useSelector((state: RootState) => state.user.profile)
//   const { isAuthenticated, userDetails } = useAuth();
//   const location = useLocation();

  // If not authenticated, redirect to login
  if (!profile) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  // Check if user's role is in the allowed roles
  const hasAccess = profile?.roles !== undefined ? allowedRoles.includes(profile.roles[0] as Role) || allowedRoles.includes(profile.roles[1] as Role) : false;
  console.log("hasAccess: ", hasAccess);
  // If user doesn't have access, redirect
  if (!hasAccess) {
    return <Navigate to={redirectPath} replace />;
  }

  return <>{children ?? <Outlet />}</>;
};
