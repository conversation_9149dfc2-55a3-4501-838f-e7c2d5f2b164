import { useKeycloak } from '@react-keycloak/web'
import AppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import MenuItem from "@mui/material/MenuItem";
import Box from "@mui/material/Box";
import LanguageIcon from "@mui/icons-material/Language";
// import keycloak from 'src/core/keycloak'



export default function Header() {
  // const { keycloak } = useKeycloak();
  // const handleLogin = () => keycloak.login();
  // const handleLogout = () => keycloak.logout();
  // const handleRegister = () => keycloak.register()

  return (
    <AppBar
      position="fixed"
      elevation={0}
      sx={{
        background: "rgba(255,255,255,0.95)",
        color: "#1a237e",
        boxShadow: "0 2px 8px 0 rgba(16,30,54,0.04)",
        borderBottom: "1px solid #f0f1f3",
      }}
    >
      <Toolbar sx={{ minHeight: 72, px: { xs: 2, md: 8 } }}>
        {/* Logo */}
        <Typography
          variant="h5"
          sx={{
            fontWeight: 700,
            letterSpacing: 2,
            color: "#1a237e",
            fontFamily: "'Montserrat', 'Arial', sans-serif",
            mr: 4,
          }}
        >
          <span style={{ fontFamily: "monospace", fontWeight: 900, letterSpacing: 2 }}>GenEdu</span>
        </Typography>

        {/* Menu */}
        <Box sx={{ flexGrow: 1, display: "flex", gap: 2 }}>
          <MenuItem sx={{ color: "#222", fontWeight: 500 }}>Products</MenuItem>
          <MenuItem sx={{ color: "#222", fontWeight: 500 }}>Solutions</MenuItem>
          <MenuItem sx={{ color: "#222", fontWeight: 500 }}>About</MenuItem>
          <MenuItem sx={{ color: "#222", fontWeight: 500 }}>Pricing</MenuItem>
        </Box>

        {/* Right side */}
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <IconButton size="small" sx={{ color: "#222" }}>
            <LanguageIcon fontSize="small" />
            <Typography variant="body2" sx={{ ml: 0.5, fontWeight: 500 }}>English</Typography>
          </IconButton>
          <Button 
          // onClick={handleLogin} 
          sx={{ color: "#222", fontWeight: 500, textTransform: "none", mx: 1 }} disableElevation>
            Login
          </Button>
          <Button
            variant="contained"
            sx={{
              background: "#1746d6",
              color: "#fff",
              borderRadius: 999,
              px: 3,
              py: 1,
              fontWeight: 700,
              textTransform: "none",
              fontSize: 16,
              boxShadow: "none",
              "&:hover": { background: "#0d2e8b" },
            }}
          >
            Start for free
          </Button>
        </Box>
      </Toolbar>
    </AppBar>
  );
}
