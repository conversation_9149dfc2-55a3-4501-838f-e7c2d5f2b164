import { <PERSON>, Toolbar, <PERSON>po<PERSON>, Paper, Button, Grid, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton, TextField, InputAdornment, Tabs, Tab, Pagination } from '@mui/material'
import AdminSidebar from '../../components/Admin/AdminSidebar'
import AdminHeader from '../../components/Admin/AdminHeader'
import SearchIcon from '@mui/icons-material/Search'
import PlayArrowIcon from '@mui/icons-material/PlayArrow'
import PauseIcon from '@mui/icons-material/Pause'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'
import FilterListIcon from '@mui/icons-material/FilterList'
import AddIcon from '@mui/icons-material/Add'
import { useState } from 'react'

// Sample data for audio lessons
const audioLessons = [
  { 
    id: 1, 
    title: "Introduction to React Hooks", 
    duration: "12:45", 
    created: "2023-06-18", 
    status: "published",
    category: "Programming",
    views: 1245
  },
  { 
    id: 2, 
    title: "Advanced CSS Techniques", 
    duration: "18:30", 
    created: "2023-06-17", 
    status: "processing",
    category: "Web Design",
    views: 890
  },
  { 
    id: 3, 
    title: "JavaScript ES6 Features", 
    duration: "15:20", 
    created: "2023-06-15", 
    status: "published",
    category: "Programming",
    views: 2100
  },
  { 
    id: 4, 
    title: "Responsive Design Principles", 
    duration: "22:10", 
    created: "2023-06-14", 
    status: "published",
    category: "Web Design",
    views: 1560
  },
  { 
    id: 5, 
    title: "Node.js Fundamentals", 
    duration: "28:45", 
    created: "2023-06-12", 
    status: "draft",
    category: "Backend",
    views: 320
  },
  { 
    id: 6, 
    title: "Database Design Best Practices", 
    duration: "32:15", 
    created: "2023-06-10", 
    status: "published",
    category: "Database",
    views: 980
  },
];

export default function AudioLessons() {
  const [tabValue, setTabValue] = useState(0);
  const [playingId, setPlayingId] = useState<number | null>(null);
  const [page, setPage] = useState(1);
  
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handlePlayPause = (id: number) => {
    if (playingId === id) {
      setPlayingId(null);
    } else {
      setPlayingId(id);
    }
  };
  
  const handleChangePage = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  const getStatusColor = (status: string) => {
    switch(status) {
      case 'published': return '#4caf50';
      case 'processing': return '#ff9800';
      case 'draft': return '#9e9e9e';
      default: return '#9e9e9e';
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        minHeight: '100vh',
        width: '100vw',
        background: 'linear-gradient(135deg, #e3eafc 0%, #cfd8dc 100%)',
        overflowX: 'hidden'
      }}
    >
      <AdminHeader />
      <AdminSidebar />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          minHeight: '100vh',
          p: { xs: 2, sm: 4 }
        }}
      >
        <Toolbar />
        
        {/* Header Section */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              color: '#283593'
            }}
          >
            Audio Lessons
          </Typography>
          
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            sx={{
              backgroundColor: '#1e88e5',
              borderRadius: 2,
              px: 3,
              '&:hover': {
                backgroundColor: '#1565c0'
              }
            }}
          >
            Create New Lesson
          </Button>
        </Box>
        
        {/* Search and Filter Section */}
        <Paper
          elevation={3}
          sx={{
            p: 3,
            borderRadius: 3,
            mb: 4
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid gridColumn={{ xs: 'span 12', md: 'span 6' }}>
              <TextField
                fullWidth
                placeholder="Search audio lessons..."
                variant="outlined"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                  sx: { borderRadius: 2 }
                }}
              />
            </Grid>
            <Grid gridColumn={{ xs: 'span 12', md: 'span 6' }}>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: { xs: 'flex-start', md: 'flex-end' } }}>
                <Button
                  variant="outlined"
                  startIcon={<FilterListIcon />}
                  sx={{
                    borderRadius: 2,
                    borderColor: '#1e88e5',
                    color: '#1e88e5',
                    '&:hover': {
                      borderColor: '#1565c0',
                      backgroundColor: 'rgba(21, 101, 192, 0.04)'
                    }
                  }}
                >
                  Filter
                </Button>
                <Button
                  variant="outlined"
                  sx={{
                    borderRadius: 2,
                    borderColor: '#1e88e5',
                    color: '#1e88e5',
                    '&:hover': {
                      borderColor: '#1565c0',
                      backgroundColor: 'rgba(21, 101, 192, 0.04)'
                    }
                  }}
                >
                  Sort By: Latest
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Paper>
        
        {/* Tabs Section */}
        <Box sx={{ mb: 3 }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange}
            sx={{
              '& .MuiTabs-indicator': {
                backgroundColor: '#1e88e5',
              },
              '& .Mui-selected': {
                color: '#1e88e5',
                fontWeight: 'bold',
              }
            }}
          >
            <Tab label="All Lessons" />
            <Tab label="Published" />
            <Tab label="Processing" />
            <Tab label="Drafts" />
          </Tabs>
        </Box>
        
        {/* Table Section */}
        <TableContainer 
          component={Paper}
          sx={{
            borderRadius: 3,
            overflow: 'hidden',
            mb: 3,
            boxShadow: 3
          }}
        >
          <Table>
            <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>Title</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Duration</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Category</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Created</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Views</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {audioLessons.map((lesson) => (
                <TableRow key={lesson.id} hover>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <IconButton 
                        size="small" 
                        onClick={() => handlePlayPause(lesson.id)}
                        sx={{ 
                          mr: 1, 
                          backgroundColor: '#e3f2fd',
                          '&:hover': { backgroundColor: '#bbdefb' }
                        }}
                      >
                        {playingId === lesson.id ? <PauseIcon /> : <PlayArrowIcon />}
                      </IconButton>
                      <Typography variant="body2" fontWeight="medium">
                        {lesson.title}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{lesson.duration}</TableCell>
                  <TableCell>{lesson.category}</TableCell>
                  <TableCell>{lesson.created}</TableCell>
                  <TableCell>{lesson.views.toLocaleString()}</TableCell>
                  <TableCell>
                    <Chip 
                      label={lesson.status} 
                      size="small"
                      sx={{ 
                        backgroundColor: `${getStatusColor(lesson.status)}20`,
                        color: getStatusColor(lesson.status),
                        fontWeight: 'medium',
                        textTransform: 'capitalize'
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex' }}>
                      <IconButton size="small" sx={{ color: '#1e88e5' }}>
                        <EditIcon fontSize="small" />
                      </IconButton>
                      <IconButton size="small" sx={{ color: '#f44336' }}>
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        {/* Pagination */}
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <Pagination 
            count={10} 
            page={page} 
            onChange={handleChangePage} 
            color="primary"
            sx={{
              '& .MuiPaginationItem-root': {
                borderRadius: 2
              }
            }}
          />
        </Box>
      </Box>
    </Box>
  )
}
