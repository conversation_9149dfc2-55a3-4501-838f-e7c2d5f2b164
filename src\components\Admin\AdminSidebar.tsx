import { Drawer, List, ListItemButton, ListItemIcon, ListItemText, Toolbar, Box } from '@mui/material'
import DashboardIcon from '@mui/icons-material/Dashboard'
import AudiotrackIcon from '@mui/icons-material/Audiotrack'
import VideoLibraryIcon from '@mui/icons-material/VideoLibrary'
import SettingsIcon from '@mui/icons-material/Settings'
import { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'

const drawerWidth = 220
const miniWidth = 64

const menu = [
  { icon: <DashboardIcon />, label: 'Dashboard', path: '/admin' },
  { icon: <AudiotrackIcon />, label: 'Audio Lessons', path: '/admin/audio-lessons' },
  { icon: <VideoLibraryIcon />, label: 'Video Lessons', path: '/admin/video-lessons' },
  { icon: <SettingsIcon />, label: 'Settings', path: '/admin/settings' }
]

export default function AdminSidebar() {
  const [open, setOpen] = useState(true) // Mặc định để mở
  const [selected, setSelected] = useState(0)
  const navigate = useNavigate()
  const location = useLocation()
  
  // Update selected menu item based on current path
  useEffect(() => {
    const currentPath = location.pathname
    const index = menu.findIndex(item => currentPath === item.path)
    if (index !== -1) {
      setSelected(index)
    }
  }, [location.pathname])

  const handleMenuClick = (index: number, path: string) => {
    setSelected(index)
    navigate(path)
  }

  return (
    <Box
      onMouseEnter={() => setOpen(true)}
      onMouseLeave={() => setOpen(false)}
      sx={{ height: '100vh' }}
    >
      <Drawer
        variant="permanent"
        open={open}
        sx={{
          width: open ? drawerWidth : miniWidth,
          flexShrink: 0,
          whiteSpace: 'nowrap',
          transition: (theme) =>
            theme.transitions.create('width', {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.standard // Làm chậm lại
            }),
          [`& .MuiDrawer-paper`]: {
            width: open ? drawerWidth : miniWidth,
            boxSizing: 'border-box',
            background: 'linear-gradient(180deg, #1a237e 80%, #3949ab 100%)',
            color: '#fff',
            height: '100vh',
            overflow: 'hidden',
            transition: (theme) =>
              theme.transitions.create('width', {
                easing: theme.transitions.easing.sharp,
                duration: theme.transitions.duration.standard // Làm chậm lại
              })
          }
        }}
      >
        <Toolbar />
        <List sx={{ mt: 2 }}>
          {menu.map((item, idx) => (
            <ListItemButton
              key={item.label}
              selected={selected === idx}
              onClick={() => handleMenuClick(idx, item.path)}
              sx={{
                borderRadius: 2,
                mx: 1,
                mb: 1,
                justifyContent: open ? 'initial' : 'center',
                background: selected === idx ? 'rgba(255,255,255,0.15)' : 'none',
                '&:hover': {
                  background: 'rgba(255,255,255,0.08)'
                }
              }}
            >
              <ListItemIcon sx={{ color: selected === idx ? '#ffd600' : '#fff', minWidth: 36 }}>
                {item.icon}
              </ListItemIcon>
              {open && (
                <ListItemText
                  primary={item.label}
                  primaryTypographyProps={{
                    fontWeight: selected === idx ? 700 : 600,
                    color: selected === idx ? '#ffd600' : '#fff'
                  }}
                />
              )}
            </ListItemButton>
          ))}
        </List>
      </Drawer>
    </Box>
  )
}
