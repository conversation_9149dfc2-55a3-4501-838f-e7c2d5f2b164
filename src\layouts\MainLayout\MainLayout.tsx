import { memo } from 'react'
import { Outlet } from 'react-router-dom'
import Footer from 'src/components/FooterCompact'
import Header from 'src/components/NavHeaderV2'
interface Props {
  children?: React.ReactNode
}
function MainLayoutInner({ children }: Props) {
  return (
    <div>
      <Header />
      {children}
      <Outlet />
      <Footer />
    </div>
  )
}
const MainLayout = memo(MainLayoutInner)
export default MainLayout
