import type { AuthClientError } from '@react-keycloak/core'
import { Suspense, useState } from 'react'
import { AppContext } from 'src/contexts/app.context'
import KeycloakProviderWithInit from 'src/core/keycloak/KeycloakProviderWithInit'
import type { KeycloakAuthenticatedData } from 'src/types/keycloak.type'
import Home from 'src/pages/Home'
import { useDispatch, useSelector } from 'react-redux'
import type { RootState } from 'src/store'
import type { User } from 'src/types/user.type'
import { clearProfile, setProfile } from 'src/store/userSlice'
import keycloak from 'src/core/keycloak'
import useRouteElements from './useRouteElements'
import RoleRedirect from './guards/RoleRedirect'
import Loader from './components/Loader/Loader'
import ErrorBoundary from './components/ErrorBoundary'
import { PersistGate } from 'redux-persist/integration/react'
import { persistor } from './store/store'

function App() {
  // Simplified app context state
  const [keycloakAuth, setKeycloakAuth] = useState<KeycloakAuthenticatedData | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  // Redux hooks
  const dispatch = useDispatch()
  const profile = useSelector((state: RootState) => state.user.profile)

  // Event handler for Keycloak events
  const handleKeycloakEvent = (event: string, error: AuthClientError | undefined) => {
    console.log('Keycloak event:', event)
    console.log('user profile:', profile)
    if (error) {
      console.error('Keycloak error:', error)
    }

    // You can add your authentication logic here if needed
    if (event === 'onAuthSuccess') {
      setIsAuthenticated(true)
      console.log('User is authenticated')
      // Lấy thông tin user từ keycloak và dispatch setProfile
      const token = keycloak.tokenParsed
      const realmRoles = token?.realm_access?.roles || []
      const roles: User['roles'] = []
      if (realmRoles.includes('genedu-admin')) roles.push('Admin' as User['roles'][number])
      if (realmRoles.includes('genedu-user')) roles.push('User' as User['roles'][number])
      if (token) {
        const user: User = {
          _id: token.sub || '',
          username: token.preferred_username || '',
          roles: roles,
          email: token.email || '',
          name: token.name || '',
          date_of_birth: token.date_of_birth || '',
          gender: token.gender as User['gender'] | undefined,
          avatar: token.avatar || '',
          address: token.address || '',
          phone: token.phone_number || '',
          createdAt: '', // Không có trong token, cần lấy từ API nếu cần
          updatedAt: '' // Không có trong token, cần lấy từ API nếu cần
        }
        dispatch(setProfile(user))
      }
      console.log('Username:', keycloak.tokenParsed?.preferred_username)
    }
    if (event === 'onAuthLogout') {
      setIsAuthenticated(false)
      dispatch(clearProfile())
      console.log('User is logged out')
    }
  }

  const routeElements = useRouteElements()

  return (
    <AppContext.Provider
      value={{
        keycloakAuth,
        setKeycloakAuth,
        isAuthenticated,
        setIsAuthenticated,
        profile: profile, // or provide a suitable initial value
        setProfile: (user: User) => dispatch(setProfile(user)),
        reset: () => dispatch(clearProfile())
      }}
    >
      <PersistGate loading={null} persistor={persistor}>
        {/* <KeycloakProviderWithInit onEvent={handleKeycloakEvent}> */}
          {/* <RoleRedirect /> */}
          <Suspense fallback={<Loader />}>
            <ErrorBoundary>
              {routeElements}
            </ErrorBoundary>
          </Suspense>
        {/* </KeycloakProviderWithInit> */}
      </PersistGate>
    </AppContext.Provider>
  )
}

export default App
