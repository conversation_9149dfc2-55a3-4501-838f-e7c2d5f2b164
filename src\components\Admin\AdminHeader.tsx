import { AppBar, Toolbar, Typography, Box, Avatar } from '@mui/material'

export default function AdminHeader() {
  return (
    <AppBar
      position="fixed"
      elevation={1}
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 1,
        background: 'linear-gradient(90deg, #283593 60%, #5c6bc0 100%)',
        height: 64,
        borderBottom: '1px solid rgba(255, 255, 255, 0.15)',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}
    >
      <Toolbar sx={{ px: { xs: 2, sm: 3 } }}>
        <Typography variant="h6" noWrap sx={{ flexGrow: 1, fontWeight: 700, letterSpacing: 1, fontSize: '1.25rem' }}>
          AI System for Auto-generating Audio/Video Lessons
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
          <Avatar alt="Admin" src="/static/images/avatar/1.jpg" sx={{ bgcolor: '#fff', color: '#283593' }} />
          <Typography variant="body1" sx={{ fontWeight: 600 }}>
            Admin
          </Typography>
        </Box>
      </Toolbar>
    </AppBar>
  )
}
