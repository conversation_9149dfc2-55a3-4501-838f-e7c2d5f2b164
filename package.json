{"name": "react-starter", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": "18.x"}, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "prettier:fix": "prettier --write \"src/**/(*.tsx|*.ts|*.css|*.scss)\""}, "dependencies": {"@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@floating-ui/react": "^0.24.6", "@mui/icons-material": "^7.1.0", "@mui/joy": "^5.0.0-beta.52", "@mui/material": "^7.1.0", "@react-keycloak/core": "^3.2.0", "@react-keycloak/web": "^3.4.0", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.76.1", "antd": "^5.25.2", "axios": "^1.9.0", "framer-motion": "^10.12.18", "i18next": "^23.2.8", "keycloak-js": "^26.1.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.0.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "redux-persist": "^6.0.0", "scss": "^0.2.4", "styled-components": "^6.1.18", "tailwindcss": "^4.1.8", "yup": "^1.2.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^20.4.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-storybook": "^0.9.0", "globals": "^16.0.0", "msw": "^2.9.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "msw": {"workerDirectory": ["public"]}}