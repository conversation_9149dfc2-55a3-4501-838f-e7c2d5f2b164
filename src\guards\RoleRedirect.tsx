import { useEffect } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import type { RootState } from 'src/store'

const RoleRedirect = () => {
  const user = useSelector((state: RootState) => state.user)
  const roles = user?.profile?.roles || []
  const navigate = useNavigate()

  useEffect(() => {
    if (user) {
      if (roles.includes('Admin')) {
        navigate('/admin')
      } else if (roles.includes('User')) {
        navigate('/home')
      } else {
        navigate('/') // fallback nếu không có quyền
      }
    }
  }, [user])

  return null // không hiển thị gì cả
}

export default RoleRedirect
