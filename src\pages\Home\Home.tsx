import Header from 'src/components/home/<USER>'
import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import IconButton from '@mui/material/IconButton'
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew'
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos'
import { useState, useEffect } from 'react'

const heroImages = [
  'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=800&q=80'
]

export default function Home() {
  
  const [current, setCurrent] = useState(0)
  const handlePrev = () => setCurrent((prev) => (prev === 0 ? heroImages.length - 1 : prev - 1))
  const handleNext = () => setCurrent((prev) => (prev === heroImages.length - 1 ? 0 : prev + 1))

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrent((prev) => (prev === heroImages.length - 1 ? 0 : prev + 1))
    }, 3500)
    return () => clearInterval(interval)
  }, [])

  return (
    <>
      <Header />
      {/* Hero Section */}
      <Box
        sx={{
          width: '100%',
          minHeight: '70vh',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: '#f6f8fa',
          pt: 8,
          pb: 6
        }}
      >
        <Typography
          variant='h2'
          sx={{
            fontWeight: 800,
            fontSize: { xs: 36, md: 56 },
            textAlign: 'center',
            mb: 3,
            letterSpacing: -1,
            color: '#1a237e'
          }}
        >
          Create beautiful presentations in seconds
        </Typography>
        <Typography
          variant='h5'
          sx={{
            color: '#555',
            mb: 4,
            textAlign: 'center',
            maxWidth: 600
          }}
        >
          Powered by AI. Fast, easy, and stunning. Just type a prompt and GenEdu does the rest.
        </Typography>
        <Button
          variant='contained'
          sx={{
            background: '#1746d6',
            color: '#fff',
            borderRadius: 999,
            px: 5,
            py: 1.5,
            fontWeight: 700,
            fontSize: 20,
            boxShadow: 'none',
            mb: 6,
            '&:hover': { background: '#0d2e8b' }
          }}
        >
          Try for free
        </Button>
        <Box
          sx={{
            width: { xs: '95%', md: '70%' },
            height: { xs: 220, md: 400 },
            background: '#e3e7ef',
            borderRadius: 6,
            mt: 4,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
            boxShadow: 2,
            overflow: 'hidden'
          }}
        >
          <IconButton
            onClick={handlePrev}
            sx={{
              position: 'absolute',
              left: 8,
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 2,
              bgcolor: 'rgba(255,255,255,0.7)',
              '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' }
            }}
          >
            <ArrowBackIosNewIcon />
          </IconButton>
          <Box
            component='img'
            src={heroImages[current]}
            alt={`slide-${current}`}
            sx={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              borderRadius: 3,
              transition: 'all 0.4s'
            }}
          />
          <IconButton
            onClick={handleNext}
            sx={{
              position: 'absolute',
              right: 8,
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 2,
              bgcolor: 'rgba(255,255,255,0.7)',
              '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' }
            }}
          >
            <ArrowForwardIosIcon />
          </IconButton>
          <Box
            sx={{
              position: 'absolute',
              bottom: 12,
              left: '50%',
              transform: 'translateX(-50%)',
              display: 'flex',
              gap: 1
            }}
          >
            {heroImages.map((_, idx) => (
              <Box
                key={idx}
                sx={{
                  width: 10,
                  height: 10,
                  borderRadius: '50%',
                  bgcolor: idx === current ? '#1746d6' : '#b0b6c3',
                  transition: 'all 0.3s'
                }}
              />
            ))}
          </Box>
        </Box>
      </Box>

      {/* Features Section */}
      <Box
        sx={{
          bgcolor: '#fff',
          py: 8,
          px: { xs: 2, md: 8 },
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          gap: 4,
          justifyContent: 'center',
          alignItems: 'stretch'
        }}
      >
        <Box
          sx={{
            flex: 1,
            bgcolor: '#f6f8fa',
            borderRadius: 4,
            p: 4,
            boxShadow: 1,
            minWidth: 260
          }}
        >
          <Typography variant='h5' sx={{ fontWeight: 700, mb: 1 }}>
            AI-powered creation
          </Typography>
          <Typography>Generate presentations, docs, and webpages instantly from a prompt.</Typography>
        </Box>
        <Box
          sx={{
            flex: 1,
            bgcolor: '#f6f8fa',
            borderRadius: 4,
            p: 4,
            boxShadow: 1,
            minWidth: 260
          }}
        >
          <Typography variant='h5' sx={{ fontWeight: 700, mb: 1 }}>
            Beautiful by default
          </Typography>
          <Typography>Stunning, modern designs with zero effort. No design skills needed.</Typography>
        </Box>
        <Box
          sx={{
            flex: 1,
            bgcolor: '#f6f8fa',
            borderRadius: 4,
            p: 4,
            boxShadow: 1,
            minWidth: 260
          }}
        >
          <Typography variant='h5' sx={{ fontWeight: 700, mb: 1 }}>
            Flexible & interactive
          </Typography>
          <Typography>Embed anything, add interactive elements, and customize your way.</Typography>
        </Box>
      </Box>

      {/* Call to Action */}
      <Box
        sx={{
          textAlign: 'center',
          py: 8,
          bgcolor: '#f6f8fa'
        }}
      >
        <Typography variant='h4' sx={{ fontWeight: 800, mb: 3, color: '#1a237e' }}>
          Ready to get started?
        </Typography>
        <Button
          variant='contained'
          sx={{
            background: '#1746d6',
            color: '#fff',
            borderRadius: 999,
            px: 5,
            py: 1.5,
            fontWeight: 700,
            fontSize: 20,
            boxShadow: 'none',
            '&:hover': { background: '#0d2e8b' }
          }}
        >
          Try Gamma for free
        </Button>
      </Box>

      {/* Footer */}
      <Box
        sx={{
          py: 4,
          bgcolor: '#f0f1f3',
          color: '#888',
          textAlign: 'center',
          fontSize: 16
        }}
      >
        © {new Date().getFullYear()} Gamma. All rights reserved.
      </Box>
    </>
  )
}
