import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import tailwindcss from '@tailwindcss/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  server: {
    host: true,
    port: 4000,
    watch: {
      usePolling: true
    }
  },
  resolve: {
    alias: {
      src: path.resolve(__dirname, './src')
    }
  }
})
