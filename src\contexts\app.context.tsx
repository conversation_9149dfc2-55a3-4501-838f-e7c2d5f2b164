//contexts
import React, { createContext, useState } from 'react'
import type { KeycloakAuthenticatedData } from 'src/types/keycloak.type'
import type { User, UserResponse } from 'src/types/user.type'
import { getAccessTokenFromLS, getProfileFromLS } from 'src/utils/auth'

interface AppContextInterface {
  isAuthenticated: boolean
  setIsAuthenticated: React.Dispatch<React.SetStateAction<boolean>>
  profile: User | null
  setProfile: ((user: User) => void) | React.Dispatch<React.SetStateAction<User | null>>

  //Keycloak Auth
  keycloakAuth: KeycloakAuthenticatedData | null
  setKeycloakAuth: React.Dispatch<React.SetStateAction<KeycloakAuthenticatedData | null>>

  reset: () => void
}

export const getInitialAppContext: () => AppContextInterface = () => ({
  isAuthenticated: Boolean(getAccessTokenFromLS()),
  setIsAuthenticated: () => null,
  profile: getProfileFromLS(),
  setProfile: () => null,
  extendedPurchases: [],
  setExtendedPurchases: () => null,
  keycloakAuth: null,
  setKeycloakAuth: () => null,
  reset: () => null
})

const initialAppContext = getInitialAppContext()

export const AppContext = createContext<AppContextInterface>(initialAppContext)

export const AppProvider = ({
  children,
  defaultValue = initialAppContext
}: {
  children: React.ReactNode
  defaultValue?: AppContextInterface
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(defaultValue.isAuthenticated)
  const [profile, setProfile] = useState<User | null>(defaultValue.profile)
  const [keycloakAuth, setKeycloakAuth] = useState<KeycloakAuthenticatedData | null>(defaultValue.keycloakAuth)

  const reset = () => {
    setIsAuthenticated(false)
    setKeycloakAuth(null)
    setProfile(null)
  }

  return (
    <AppContext.Provider
      value={{
        isAuthenticated,
        setIsAuthenticated,
        profile,
        setProfile,
        keycloakAuth,
        setKeycloakAuth,
        reset
      }}
    >
      {children}
    </AppContext.Provider>
  )
}
