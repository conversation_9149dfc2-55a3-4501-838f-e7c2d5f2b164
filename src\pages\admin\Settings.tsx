import { useState } from 'react'
import { Box, Toolbar, Typography, Paper, Button, Grid, Switch, TextField, Divider, FormControlLabel, Tabs, Tab, Select, MenuItem, FormControl, InputLabel, Slider, Alert } from '@mui/material'
import AdminSidebar from '../../components/Admin/AdminSidebar'
import AdminHeader from '../../components/Admin/AdminHeader'
import SaveIcon from '@mui/icons-material/Save'
import CloudUploadIcon from '@mui/icons-material/CloudUpload'
import SecurityIcon from '@mui/icons-material/Security'
import NotificationsIcon from '@mui/icons-material/Notifications'
import StorageIcon from '@mui/icons-material/Storage'
import ApiIcon from '@mui/icons-material/Api'
import LanguageIcon from '@mui/icons-material/Language'

// TabPanel component
function TabPanel(props: { children?: React.ReactNode; index: number; value: number }) {
  const { children, value, index, ...other } = props

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  )
}

export default function Settings() {
  const [tabValue, setTabValue] = useState(0)
  const [saveSuccess, setSaveSuccess] = useState(false)
  const [apiKey, setApiKey] = useState('sk-1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t')
  const [modelQuality, setModelQuality] = useState(70)
  const [maxDuration, setMaxDuration] = useState(60)
  const [language, setLanguage] = useState('en')
  // Thêm state cho các dropdown
  const [textToSpeechModel, setTextToSpeechModel] = useState('gpt-4')
  const [videoGenerationModel, setVideoGenerationModel] = useState('sora')
  const [storageProvider, setStorageProvider] = useState('aws')
  const [dateFormat, setDateFormat] = useState('mdy')
  const [timeFormat, setTimeFormat] = useState('12h')
  const [timezone, setTimezone] = useState('utc')
  const [passwordPolicy, setPasswordPolicy] = useState('strong')
  
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue)
  }
  
  const handleSaveSettings = () => {
    // Simulate saving settings
    setSaveSuccess(true)
    setTimeout(() => setSaveSuccess(false), 3000)
  }

  return (
    <Box
      sx={{
        display: 'flex',
        minHeight: '100vh',
        width: '100vw',
        background: 'linear-gradient(135deg, #e3eafc 0%, #cfd8dc 100%)',
        overflowX: 'hidden'
      }}
    >
      <AdminHeader />
      <AdminSidebar />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          minHeight: '100vh',
          p: { xs: 2, sm: 4 }
        }}
      >
        <Toolbar />
        
        {/* Header Section */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              color: '#283593'
            }}
          >
            System Settings
          </Typography>
          
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSaveSettings}
            sx={{
              backgroundColor: '#1e88e5',
              borderRadius: 2,
              px: 3,
              '&:hover': {
                backgroundColor: '#1565c0'
              }
            }}
          >
            Save Changes
          </Button>
        </Box>
        
        {saveSuccess && (
          <Alert 
            severity="success" 
            sx={{ mb: 3, borderRadius: 2 }}
            onClose={() => setSaveSuccess(false)}
          >
            Settings saved successfully!
          </Alert>
        )}
        
        {/* Settings Content */}
        <Paper
          elevation={3}
          sx={{
            borderRadius: 3,
            overflow: 'hidden'
          }}
        >
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs 
              value={tabValue} 
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                '& .MuiTabs-indicator': {
                  backgroundColor: '#1e88e5',
                },
                '& .Mui-selected': {
                  color: '#1e88e5',
                  fontWeight: 'bold',
                },
                px: 2,
                pt: 1
              }}
            >
              <Tab icon={<ApiIcon />} label="AI Models" iconPosition="start" />
              <Tab icon={<StorageIcon />} label="Storage" iconPosition="start" />
              <Tab icon={<SecurityIcon />} label="Security" iconPosition="start" />
              <Tab icon={<NotificationsIcon />} label="Notifications" iconPosition="start" />
              <Tab icon={<LanguageIcon />} label="Localization" iconPosition="start" />
            </Tabs>
          </Box>
          
          {/* AI Models Settings */}
          <TabPanel value={tabValue} index={0}>
            <Box sx={{ px: 3 }}>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                AI Model Configuration
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Configure the AI models used for generating audio and video content.
              </Typography>
              
              <Grid container spacing={4} sx={{ mt: 1 }}>
                <Grid gridColumn={{ xs: 'span 12', md: 'span 6' }}>
                  <TextField
                    fullWidth
                    label="OpenAI API Key"
                    variant="outlined"
                    type="password"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    sx={{ mb: 3 }}
                  />
                  
                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel>Text-to-Speech Model</InputLabel>
                    <Select
                      value={textToSpeechModel}
                      onChange={(e) => setTextToSpeechModel(e.target.value)}
                      label="Text-to-Speech Model"
                    >
                      <MenuItem value="gpt-4">GPT-4 (Best Quality)</MenuItem>
                      <MenuItem value="gpt-3.5">GPT-3.5 (Balanced)</MenuItem>
                      <MenuItem value="gpt-3">GPT-3 (Faster)</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel>Video Generation Model</InputLabel>
                    <Select
                      value={videoGenerationModel}
                      onChange={(e) => setVideoGenerationModel(e.target.value)}
                      label="Video Generation Model"
                    >
                      <MenuItem value="sora">Sora (High Quality)</MenuItem>
                      <MenuItem value="midjourney">Midjourney (Balanced)</MenuItem>
                      <MenuItem value="stable-diffusion">Stable Diffusion (Faster)</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid gridColumn={{ xs: 'span 12', md: 'span 6' }}>
                  <Typography gutterBottom>Output Quality (Higher = Better Quality but Slower)</Typography>
                  <Slider
                    value={modelQuality}
                    onChange={(_e, newValue) => setModelQuality(newValue as number)}
                    valueLabelDisplay="auto"
                    step={10}
                    marks
                    min={10}
                    max={100}
                    sx={{ mb: 4 }}
                  />
                  
                  <Typography gutterBottom>Maximum Video Duration (seconds)</Typography>
                  <Slider
                    value={maxDuration}
                    onChange={(_e, newValue) => setMaxDuration(newValue as number)}
                    valueLabelDisplay="auto"
                    step={30}
                    marks
                    min={30}
                    max={300}
                    sx={{ mb: 4 }}
                  />
                  
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Enable GPU Acceleration"
                    sx={{ mb: 1, display: 'block' }}
                  />
                  
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Use Cached Results When Possible"
                    sx={{ mb: 1, display: 'block' }}
                  />
                </Grid>
              </Grid>
              
              <Divider sx={{ my: 4 }} />
              
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                API Usage & Limits
              </Typography>
              
              <Grid container spacing={3}>
                <Grid gridColumn={{ xs: 'span 12', md: 'span 4' }}>
                  <Paper
                    elevation={0}
                    sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 2, textAlign: 'center' }}
                  >
                    <Typography variant="body2" color="text.secondary">
                      API Calls This Month
                    </Typography>
                    <Typography variant="h4" fontWeight="bold" color="primary">
                      1,234
                    </Typography>
                  </Paper>
                </Grid>
                <Grid gridColumn={{ xs: 'span 12', md: 'span 4' }}>
                  <Paper
                    elevation={0}
                    sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 2, textAlign: 'center' }}
                  >
                    <Typography variant="body2" color="text.secondary">
                      Remaining Credits
                    </Typography>
                    <Typography variant="h4" fontWeight="bold" color="primary">
                      $45.67
                    </Typography>
                  </Paper>
                </Grid>
                <Grid gridColumn={{ xs: 'span 12', md: 'span 4' }}>
                  <Paper
                    elevation={0}
                    sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 2, textAlign: 'center' }}
                  >
                    <Typography variant="body2" color="text.secondary">
                      Average Response Time
                    </Typography>
                    <Typography variant="h4" fontWeight="bold" color="primary">
                      1.2s
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </Box>
          </TabPanel>
          
          {/* Storage Settings */}
          <TabPanel value={tabValue} index={1}>
            <Box sx={{ px: 3 }}>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Storage Configuration
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Configure where and how your generated content is stored.
              </Typography>
              
              <Grid container spacing={4}>
                <Grid gridColumn={{ xs: 'span 12', md: 'span 6' }}>
                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel>Storage Provider</InputLabel>
                    <Select
                      value={storageProvider}
                      onChange={(e) => setStorageProvider(e.target.value)}
                      label="Storage Provider"
                    >
                      <MenuItem value="aws">Amazon S3</MenuItem>
                      <MenuItem value="gcp">Google Cloud Storage</MenuItem>
                      <MenuItem value="azure">Azure Blob Storage</MenuItem>
                      <MenuItem value="local">Local Storage</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <TextField
                    fullWidth
                    label="Bucket Name / Container"
                    variant="outlined"
                    defaultValue="genedu-media-assets"
                    sx={{ mb: 3 }}
                  />
                  
                  <TextField
                    fullWidth
                    label="Region"
                    variant="outlined"
                    defaultValue="us-east-1"
                    sx={{ mb: 3 }}
                  />
                </Grid>
                
                <Grid gridColumn={{ xs: 'span 12', md: 'span 6' }}>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Enable CDN Distribution"
                    sx={{ mb: 2, display: 'block' }}
                  />
                  
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Auto-delete Temporary Files"
                    sx={{ mb: 2, display: 'block' }}
                  />
                  
                  <FormControlLabel
                    control={<Switch />}
                    label="Enable Versioning"
                    sx={{ mb: 2, display: 'block' }}
                  />
                  
                  <Button
                    variant="outlined"
                    startIcon={<CloudUploadIcon />}
                    sx={{ mt: 2 }}
                  >
                    Test Connection
                  </Button>
                </Grid>
              </Grid>
              
              <Divider sx={{ my: 4 }} />
              
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Storage Usage
              </Typography>
              
              <Grid container spacing={3}>
                <Grid gridColumn={{ xs: 'span 12', md: 'span 4' }}>
                  <Paper
                    elevation={0}
                    sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 2, textAlign: 'center' }}
                  >
                    <Typography variant="body2" color="text.secondary">
                      Total Storage Used
                    </Typography>
                    <Typography variant="h4" fontWeight="bold" color="primary">
                      24.7 GB
                    </Typography>
                  </Paper>
                </Grid>
                <Grid gridColumn={{ xs: 'span 12', md: 'span 4' }}>
                  <Paper
                    elevation={0}
                    sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 2, textAlign: 'center' }}
                  >
                    <Typography variant="body2" color="text.secondary">
                      Video Files
                    </Typography>
                    <Typography variant="h4" fontWeight="bold" color="primary">
                      18.3 GB
                    </Typography>
                  </Paper>
                </Grid>
                <Grid gridColumn={{ xs: 'span 12', md: 'span 4' }}>
                  <Paper
                    elevation={0}
                    sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 2, textAlign: 'center' }}
                  >
                    <Typography variant="body2" color="text.secondary">
                      Audio Files
                    </Typography>
                    <Typography variant="h4" fontWeight="bold" color="primary">
                      6.4 GB
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </Box>
          </TabPanel>
          
          {/* Security Settings */}
          <TabPanel value={tabValue} index={2}>
            <Box sx={{ px: 3 }}>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Security Settings
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Configure security settings for your application.
              </Typography>
              
              <Grid container spacing={4}>
                <Grid gridColumn={{ xs: 'span 12', md: 'span 6' }}>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Enable Two-Factor Authentication"
                    sx={{ mb: 2, display: 'block' }}
                  />
                  
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="API Key Authentication"
                    sx={{ mb: 2, display: 'block' }}
                  />
                  
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="HTTPS Enforcement"
                    sx={{ mb: 2, display: 'block' }}
                  />
                  
                  <FormControlLabel
                    control={<Switch />}
                    label="IP Whitelisting"
                    sx={{ mb: 2, display: 'block' }}
                  />
                </Grid>
                
                <Grid gridColumn={{ xs: 'span 12', md: 'span 6' }}>
                  <TextField
                    fullWidth
                    label="Session Timeout (minutes)"
                    type="number"
                    defaultValue="30"
                    variant="outlined"
                    sx={{ mb: 3 }}
                  />
                  
                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel>Password Policy</InputLabel>
                    <Select
                      value={passwordPolicy}
                      onChange={(e) => setPasswordPolicy(e.target.value)}
                      label="Password Policy"
                    >
                      <MenuItem value="basic">Basic (8+ characters)</MenuItem>
                      <MenuItem value="medium">Medium (8+ chars, mixed case)</MenuItem>
                      <MenuItem value="strong">Strong (8+ chars, mixed case, numbers, symbols)</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <Button
                    variant="outlined"
                    color="error"
                    sx={{ mt: 2 }}
                  >
                    Revoke All API Keys
                  </Button>
                </Grid>
              </Grid>
            </Box>
          </TabPanel>
          
          {/* Notifications Settings */}
          <TabPanel value={tabValue} index={3}>
            <Box sx={{ px: 3 }}>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Notification Settings
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Configure how and when notifications are sent.
              </Typography>
              
              <Typography variant="subtitle1" fontWeight="bold" sx={{ mt: 3, mb: 2 }}>
                Email Notifications
              </Typography>
              
              <Grid container spacing={2}>
                <Grid gridColumn={{ xs: 'span 12' }}>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Send email when video generation completes"
                    sx={{ display: 'block' }}
                  />
                  
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Send email when audio generation completes"
                    sx={{ display: 'block' }}
                  />
                  
                  <FormControlLabel
                    control={<Switch />}
                    label="Send weekly usage reports"
                    sx={{ display: 'block' }}
                  />
                  
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Send security alerts"
                    sx={{ display: 'block' }}
                  />
                </Grid>
              </Grid>
              
              <Typography variant="subtitle1" fontWeight="bold" sx={{ mt: 4, mb: 2 }}>
                In-App Notifications
              </Typography>
              
              <Grid container spacing={2}>
                <Grid gridColumn={{ xs: 'span 12' }}>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Show notifications for completed tasks"
                    sx={{ display: 'block' }}
                  />
                  
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Show notifications for errors"
                    sx={{ display: 'block' }}
                  />
                  
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Show system announcements"
                    sx={{ display: 'block' }}
                  />
                </Grid>
              </Grid>
              
              <Typography variant="subtitle1" fontWeight="bold" sx={{ mt: 4, mb: 2 }}>
                Webhook Notifications
              </Typography>
              
              <TextField
                fullWidth
                label="Webhook URL"
                variant="outlined"
                placeholder="https://your-app.com/webhook"
                sx={{ mb: 3 }}
              />
              
              <FormControlLabel
                control={<Switch />}
                label="Enable webhook notifications"
                sx={{ display: 'block' }}
              />
            </Box>
          </TabPanel>
          
          {/* Localization Settings */}
          <TabPanel value={tabValue} index={4}>
            <Box sx={{ px: 3 }}>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Localization Settings
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Configure language and regional settings.
              </Typography>
              
              <Grid container spacing={4}>
                <Grid gridColumn={{ xs: 'span 12', md: 'span 6' }}>
                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel>Default Language</InputLabel>
                    <Select
                      value={language}
                      onChange={(e) => setLanguage(e.target.value)}
                      label="Default Language"
                    >
                      <MenuItem value="en">English</MenuItem>
                      <MenuItem value="vi">Vietnamese</MenuItem>
                      <MenuItem value="jp">Japanese</MenuItem>
                      <MenuItem value="fr">French</MenuItem>
                      <MenuItem value="de">German</MenuItem>
                      <MenuItem value="es">Spanish</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel>Date Format</InputLabel>
                    <Select
                      value={dateFormat}
                      onChange={(e) => setDateFormat(e.target.value)}
                      label="Date Format"
                    >
                      <MenuItem value="mdy">MM/DD/YYYY</MenuItem>
                      <MenuItem value="dmy">DD/MM/YYYY</MenuItem>
                      <MenuItem value="ymd">YYYY/MM/DD</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel>Time Format</InputLabel>
                    <Select
                      value={timeFormat}
                      onChange={(e) => setTimeFormat(e.target.value)}
                      label="Time Format"
                    >
                      <MenuItem value="12h">12-hour (AM/PM)</MenuItem>
                      <MenuItem value="24h">24-hour</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid gridColumn={{ xs: 'span 12', md: 'span 6' }}>
                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel>Timezone</InputLabel>
                    <Select
                      value={timezone}
                      onChange={(e) => setTimezone(e.target.value)}
                      label="Timezone"
                    >
                      <MenuItem value="utc">UTC</MenuItem>
                      <MenuItem value="est">Eastern Time (EST/EDT)</MenuItem>
                      <MenuItem value="pst">Pacific Time (PST/PDT)</MenuItem>
                      <MenuItem value="gmt+7">GMT+7 (Vietnam)</MenuItem>
                      <MenuItem value="gmt+9">GMT+9 (Japan)</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Auto-detect user's language"
                    sx={{ mb: 2, display: 'block' }}
                  />
                  
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Enable multi-language content generation"
                    sx={{ mb: 2, display: 'block' }}
                  />
                  
                  <FormControlLabel
                    control={<Switch />}
                    label="Use browser's timezone"
                    sx={{ mb: 2, display: 'block' }}
                  />
                </Grid>
              </Grid>
            </Box>
          </TabPanel>
        </Paper>
      </Box>
    </Box>
  )
}






