import { useRoutes } from 'react-router-dom'
import { lazy, useState } from 'react'
import { ProtectedRoute } from './guards/AuthGuard'
import KeycloakProviderWithInit from './core/keycloak/KeycloakProviderWithInit'
import type { KeycloakAuthenticatedData } from './types/keycloak.type'
import { useDispatch, useSelector } from 'react-redux'
import type { AuthClientError } from '@react-keycloak/core'
import keycloak from './core/keycloak'
import type { User } from './types/user.type'
import { clearProfile, setProfile } from './store/userSlice'
import type { RootState } from './store/store'

const ClientPages = {
  Home: lazy(() => import('./pages/Home/Home')),
  About: lazy(() => import('./pages/Home/About'))
}

const EducatorPages = {
  Home: lazy(() => import('./pages/educator/Homepage'))
}

const AdminPages = {
  Home: lazy(() => import('./pages/admin/Dashboard')),
  AudioLessons: lazy(() => import('./pages/admin/AudioLessons')),
  VideoLessons: lazy(() => import('./pages/admin/VideoLessons')),
  Settings: lazy(() => import('./pages/admin/Settings'))
}

export default function useRouteElements() {
  // Simplified app context state
    const [keycloakAuth, setKeycloakAuth] = useState<KeycloakAuthenticatedData | null>(null)
    const [isAuthenticated, setIsAuthenticated] = useState(false)
  
    // Redux hooks
    const dispatch = useDispatch()
    const profile = useSelector((state: RootState) => state.user)
  
    // Event handler for Keycloak events
    const handleKeycloakEvent = (event: string, error: AuthClientError | undefined) => {
    console.log('Keycloak event:', event)
    console.log('user profile:', profile)
    if (error) {
      console.error('Keycloak error:', error)
    }

    // You can add your authentication logic here if needed
    if (event === 'onAuthSuccess') {
      setIsAuthenticated(true)
      console.log('User is authenticated')
      // Lấy thông tin user từ keycloak và dispatch setProfile
      const token = keycloak.tokenParsed
      const realmRoles = token?.realm_access?.roles || []
      const roles: User['roles'] = []
      if (realmRoles.includes('genedu-admin')) roles.push('Admin' as User['roles'][number])
      if (realmRoles.includes('genedu-user')) roles.push('User' as User['roles'][number])
      if (token) {
        const user: User = {
          _id: token.sub || '',
          username: token.preferred_username || '',
          roles: roles,
          email: token.email || '',
          name: token.name || '',
          date_of_birth: token.date_of_birth || '',
          gender: token.gender as User['gender'] | undefined,
          avatar: token.avatar || '',
          address: token.address || '',
          phone: token.phone_number || '',
          createdAt: '', // Không có trong token, cần lấy từ API nếu cần
          updatedAt: '' // Không có trong token, cần lấy từ API nếu cần
        }
        dispatch(setProfile(user))
      }
      console.log('Username:', keycloak.tokenParsed?.preferred_username)
    }
    
  }
  const routeElements = useRoutes([
    // Public Routes
    {
      path: '/',
      element: (
        // <KeycloakProviderWithInit onEvent={handleKeycloakEvent}>
          <ClientPages.Home />
        // </KeycloakProviderWithInit>
      )
    },
    {
      path: '/about',
      element: <ClientPages.About />
    },
    {
      path: '/home',
      element: (
        <ProtectedRoute allowedRoles={['User']}>
          <EducatorPages.Home />
        </ProtectedRoute>
      )
    },
    {
      path: '/admin',
      children: [
        {
          path: '',
          element: (
            // <ProtectedRoute allowedRoles={['Admin']}>
              <AdminPages.Home />
            // </ProtectedRoute>
          )
        },
        {
          path: 'audio-lessons',
          element: (
            // <ProtectedRoute allowedRoles={['Admin']}>
              <AdminPages.AudioLessons />
            // </ProtectedRoute>
          )
        },
        {
          path: 'video-lessons',
          element: (
            // <ProtectedRoute allowedRoles={['Admin']}>
              <AdminPages.VideoLessons />
            // </ProtectedRoute>
          )
        },
        {
          path: 'settings',
          element: (
            // <ProtectedRoute allowedRoles={['Admin']}>
              <AdminPages.Settings />
            // </ProtectedRoute>
          )
        }
      ]
    }
  ])
  return routeElements
}
