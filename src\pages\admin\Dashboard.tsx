import { Box, Toolbar, Typography, Paper, Button, Grid, Chip, Avatar, Divider, LinearProgress } from '@mui/material'
import AdminSidebar from '../../components/Admin/AdminSidebar'
import AdminHeader from '../../components/Admin/AdminHeader'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'
import BarChartIcon from '@mui/icons-material/BarChart'
import PeopleIcon from '@mui/icons-material/People'
import AccessTimeIcon from '@mui/icons-material/AccessTime'

// Sample data
const recentLessons = [
  { id: 1, title: "Introduction to React Hooks", type: "video", date: "2023-06-18", status: "completed" },
  { id: 2, title: "Advanced CSS Techniques", type: "audio", date: "2023-06-17", status: "processing" },
  { id: 3, title: "JavaScript ES6 Features", type: "video", date: "2023-06-15", status: "completed" },
];

const stats = [
  { title: "Total Lessons", value: 42, icon: <BarChartIcon />, change: "+12%" },
  { title: "Active Users", value: 128, icon: <PeopleIcon />, change: "+18%" },
  { title: "Avg. Generation Time", value: "1.8s", icon: <AccessTimeIcon />, change: "-0.3s" },
];

export default function Dashboard() {
  return (
    <Box
      sx={{
        display: 'flex',
        minHeight: '100vh',
        width: '100vw',
        background: 'linear-gradient(135deg, #e3eafc 0%, #cfd8dc 100%)',
        overflowX: 'hidden'
      }}
    >
      <AdminHeader />
      <AdminSidebar />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          minHeight: '100vh',
          p: { xs: 2, sm: 4 }
        }}
      >
        <Toolbar />
        <Box
          sx={{
            width: '100%',
            textAlign: 'center',
            mt: 4,
            mb: 4
          }}
        >
          <Typography
            variant="h3"
            gutterBottom
            sx={{
              fontWeight: 900,
              color: '#283593',
              textShadow: '0 2px 8px rgba(44,62,80,0.1)'
            }}
          >
            Welcome to the AI Lesson Generator Dashboard
          </Typography>
          <Typography
            variant="h6"
            color="text.secondary"
            gutterBottom
            sx={{ mb: 4, fontWeight: 400 }}
          >
            Manage, generate, and review audio/video lessons from your text content using AI.
          </Typography>
        </Box>

        {/* Stats Section */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
          <Grid container spacing={3} sx={{ maxWidth: '90%' }}>
            {stats.map((stat, index) => (
              <Grid gridColumn={{ xs: 'span 12', sm: 'span 4' }} key={index}>
                <Paper
                  elevation={3}
                  sx={{
                    p: 3,
                    borderRadius: 3,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    transition: 'transform 0.3s',
                    '&:hover': { transform: 'translateY(-5px)' }
                  }}
                >
                  <Box>
                    <Typography color="text.secondary" variant="subtitle2">
                      {stat.title}
                    </Typography>
                    <Typography variant="h4" sx={{ fontWeight: 700, my: 1 }}>
                      {stat.value}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <TrendingUpIcon sx={{ fontSize: 16, color: 'success.main', mr: 0.5 }} />
                      <Typography variant="caption" color="success.main">
                        {stat.change}
                      </Typography>
                    </Box>
                  </Box>
                  <Avatar
                    sx={{
                      bgcolor: index === 0 ? '#1e88e5' : index === 1 ? '#43a047' : '#fb8c00',
                      width: 56,
                      height: 56
                    }}
                  >
                    {stat.icon}
                  </Avatar>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Main Cards */}
        <Grid container spacing={4} justifyContent="center">
          <Grid gridColumn={{ xs: 'span 12', md: 'span 6' }}>
            <Paper
              elevation={3}
              sx={{
                p: 4,
                borderRadius: 3,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                transition: 'box-shadow 0.3s',
                '&:hover': { boxShadow: 6 }
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Generate New Lesson
              </Typography>
              <Typography variant="body1" color="text.secondary" gutterBottom sx={{ mb: 3 }}>
                Convert your text into engaging audio or video lessons with just a few clicks.
              </Typography>
              
              {/* Progress stats */}
              <Box sx={{ width: '100%', mb: 4 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Monthly Usage</Typography>
                  <Typography variant="body2" fontWeight="bold">68%</Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={68} 
                  sx={{ 
                    height: 8, 
                    borderRadius: 4,
                    backgroundColor: '#e3f2fd',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: '#1e88e5'
                    }
                  }} 
                />
              </Box>
              
              <Button
                variant="contained"
                sx={{
                  mt: 'auto',
                  px: 4,
                  py: 1.2,
                  fontWeight: 600,
                  borderRadius: 2,
                  boxShadow: 2,
                  fontSize: '0.9rem',
                  backgroundColor: '#1e88e5',
                  '&:hover': {
                    backgroundColor: '#1565c0'
                  }
                }}
              >
                START GENERATING
              </Button>
            </Paper>
          </Grid>
          <Grid gridColumn={{ xs: 'span 12', md: 'span 6' }}>
            <Paper
              elevation={3}
              sx={{
                p: 4,
                borderRadius: 3,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'box-shadow 0.3s',
                '&:hover': { boxShadow: 6 }
              }}
            >
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Recent Lessons
              </Typography>
              <Typography variant="body1" color="text.secondary" gutterBottom sx={{ mb: 3 }}>
                View and manage your recently generated lessons.
              </Typography>
              
              {/* Recent lessons list */}
              <Box sx={{ width: '100%', mb: 3, flexGrow: 1 }}>
                {recentLessons.map((lesson, index) => (
                  <Box key={lesson.id}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', py: 2 }}>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">{lesson.title}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {lesson.date}
                        </Typography>
                      </Box>
                      <Chip 
                        size="small" 
                        label={lesson.type} 
                        sx={{ 
                          height: 24, 
                          backgroundColor: lesson.type === "video" ? '#1e88e5' : '#9c27b0',
                          color: 'white'
                        }}
                      />
                    </Box>
                    {index < recentLessons.length - 1 && <Divider />}
                  </Box>
                ))}
              </Box>
              
              <Button
                variant="outlined"
                sx={{
                  mt: 'auto',
                  px: 4,
                  py: 1.2,
                  fontWeight: 600,
                  borderRadius: 2,
                  fontSize: '0.9rem',
                  borderColor: '#1e88e5',
                  color: '#1e88e5',
                  '&:hover': {
                    borderColor: '#1565c0',
                    backgroundColor: 'rgba(21, 101, 192, 0.04)'
                  }
                }}
              >
                VIEW ALL LESSONS
              </Button>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </Box>
  )
}
