import { createSlice } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'
import type { User } from 'src/types/user.type'

interface UserState {
  profile: User | null
}

const initialState: UserState = {
  profile: null
}

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setProfile(state, action: PayloadAction<User>) {
      state.profile = action.payload
    },
    clearProfile(state) {
      state.profile = null
    }
  }
})

export const { setProfile, clearProfile } = userSlice.actions
export default userSlice.reducer;
