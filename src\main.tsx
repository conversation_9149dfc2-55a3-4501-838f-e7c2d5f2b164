import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'

import App from './App.tsx'
import { worker } from './msw/browser.ts'
import { BrowserRouter } from 'react-router-dom'
import TestComponents from './components/Test/TestComponents.tsx'
import { Provider } from 'react-redux'
import { store } from './store/store.ts'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 0
    }
  }
})

async function enableMocking() {
  if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'production') {
    return worker.start({
      onUnhandledRequest: 'bypass' // Don't warn about unhandled requests
    })
  }
}

// enableMocking().then(() => {
//   createRoot(document.getElementById('root')!).render(
//     <BrowserRouter>
//       <StrictMode>
//         <QueryClientProvider client={queryClient}>
//           <MainLayout>
//             <App />
//           </MainLayout>
//         </QueryClientProvider>
//       </StrictMode>
//     </BrowserRouter>
//   )
// })

createRoot(document.getElementById('root')!).render(
  <BrowserRouter>
    {/* <StrictMode> */}
    <QueryClientProvider client={queryClient}>
      <Provider store={store}>
        <App />
      </Provider>
    </QueryClientProvider>
    {/* </StrictMode> */}
  </BrowserRouter>
)
