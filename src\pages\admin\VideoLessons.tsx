import { <PERSON>, Toolbar, <PERSON>po<PERSON>, Paper, Button, Grid, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton, TextField, InputAdornment, Tabs, Tab, Pagination } from '@mui/material'
import AdminSidebar from '../../components/Admin/AdminSidebar'
import AdminHeader from '../../components/Admin/AdminHeader'
import SearchIcon from '@mui/icons-material/Search'
import PlayArrowIcon from '@mui/icons-material/PlayArrow'
import PauseIcon from '@mui/icons-material/Pause'
import EditIcon from '@mui/icons-material/Edit'
import DeleteIcon from '@mui/icons-material/Delete'
import FilterListIcon from '@mui/icons-material/FilterList'
import AddIcon from '@mui/icons-material/Add'
import VisibilityIcon from '@mui/icons-material/Visibility'
import { useState } from 'react'

// Sample data for video lessons
const videoLessons = [
  { 
    id: 1, 
    title: "React Component Lifecycle", 
    duration: "15:30", 
    created: "2023-06-20", 
    status: "published",
    category: "Programming",
    views: 2345,
    thumbnail: "https://via.placeholder.com/120x68/1e88e5/FFFFFF?text=React"
  },
  { 
    id: 2, 
    title: "CSS Grid Layout Tutorial", 
    duration: "22:15", 
    created: "2023-06-19", 
    status: "processing",
    category: "Web Design",
    views: 1120,
    thumbnail: "https://via.placeholder.com/120x68/9c27b0/FFFFFF?text=CSS"
  },
  { 
    id: 3, 
    title: "TypeScript for Beginners", 
    duration: "28:40", 
    created: "2023-06-17", 
    status: "published",
    category: "Programming",
    views: 3200,
    thumbnail: "https://via.placeholder.com/120x68/2e7d32/FFFFFF?text=TS"
  },
  { 
    id: 4, 
    title: "UI/UX Design Principles", 
    duration: "34:25", 
    created: "2023-06-15", 
    status: "published",
    category: "Design",
    views: 1870,
    thumbnail: "https://via.placeholder.com/120x68/f57c00/FFFFFF?text=UI/UX"
  },
  { 
    id: 5, 
    title: "Docker for Web Developers", 
    duration: "42:10", 
    created: "2023-06-12", 
    status: "draft",
    category: "DevOps",
    views: 560,
    thumbnail: "https://via.placeholder.com/120x68/0277bd/FFFFFF?text=Docker"
  },
  { 
    id: 6, 
    title: "GraphQL API Development", 
    duration: "38:50", 
    created: "2023-06-10", 
    status: "published",
    category: "Backend",
    views: 1450,
    thumbnail: "https://via.placeholder.com/120x68/e91e63/FFFFFF?text=GraphQL"
  },
];

export default function VideoLessons() {
  const [tabValue, setTabValue] = useState(0);
  const [playingId, setPlayingId] = useState<number | null>(null);
  const [page, setPage] = useState(1);
  
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handlePlayPause = (id: number) => {
    if (playingId === id) {
      setPlayingId(null);
    } else {
      setPlayingId(id);
    }
  };
  
  const handleChangePage = (_event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  const getStatusColor = (status: string) => {
    switch(status) {
      case 'published': return '#4caf50';
      case 'processing': return '#ff9800';
      case 'draft': return '#9e9e9e';
      default: return '#9e9e9e';
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        minHeight: '100vh',
        width: '100vw',
        background: 'linear-gradient(135deg, #e3eafc 0%, #cfd8dc 100%)',
        overflowX: 'hidden'
      }}
    >
      <AdminHeader />
      <AdminSidebar />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          minHeight: '100vh',
          p: { xs: 2, sm: 4 }
        }}
      >
        <Toolbar />
        
        {/* Header Section */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 700,
              color: '#283593'
            }}
          >
            Video Lessons
          </Typography>
          
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            sx={{
              backgroundColor: '#1e88e5',
              borderRadius: 2,
              px: 3,
              '&:hover': {
                backgroundColor: '#1565c0'
              }
            }}
          >
            Create New Video
          </Button>
        </Box>
        
        {/* Search and Filter Section */}
        <Paper
          elevation={3}
          sx={{
            p: 3,
            borderRadius: 3,
            mb: 4
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid gridColumn={{ xs: 'span 12', md: 'span 6' }}>
              <TextField
                fullWidth
                placeholder="Search video lessons..."
                variant="outlined"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                  sx: { borderRadius: 2 }
                }}
              />
            </Grid>
            <Grid gridColumn={{ xs: 'span 12', md: 'span 6' }}>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: { xs: 'flex-start', md: 'flex-end' } }}>
                <Button
                  variant="outlined"
                  startIcon={<FilterListIcon />}
                  sx={{
                    borderRadius: 2,
                    borderColor: '#1e88e5',
                    color: '#1e88e5',
                    '&:hover': {
                      borderColor: '#1565c0',
                      backgroundColor: 'rgba(21, 101, 192, 0.04)'
                    }
                  }}
                >
                  Filter
                </Button>
                <Button
                  variant="outlined"
                  sx={{
                    borderRadius: 2,
                    borderColor: '#1e88e5',
                    color: '#1e88e5',
                    '&:hover': {
                      borderColor: '#1565c0',
                      backgroundColor: 'rgba(21, 101, 192, 0.04)'
                    }
                  }}
                >
                  Sort By: Latest
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Paper>
        
        {/* Tabs Section */}
        <Box sx={{ mb: 3 }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange}
            sx={{
              '& .MuiTabs-indicator': {
                backgroundColor: '#1e88e5',
              },
              '& .Mui-selected': {
                color: '#1e88e5',
                fontWeight: 'bold',
              }
            }}
          >
            <Tab label="All Videos" />
            <Tab label="Published" />
            <Tab label="Processing" />
            <Tab label="Drafts" />
          </Tabs>
        </Box>
        
        {/* Table Section */}
        <TableContainer 
          component={Paper}
          sx={{
            borderRadius: 3,
            overflow: 'hidden',
            mb: 3,
            boxShadow: 3
          }}
        >
          <Table>
            <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>Video</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Duration</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Category</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Created</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Views</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {videoLessons.map((lesson) => (
                <TableRow key={lesson.id} hover>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box sx={{ position: 'relative', mr: 2 }}>
                        <Box
                          component="img"
                          src={lesson.thumbnail}
                          alt={lesson.title}
                          sx={{ 
                            width: 120, 
                            height: 68, 
                            borderRadius: 1,
                            objectFit: 'cover'
                          }}
                        />
                        <IconButton 
                          size="small" 
                          onClick={() => handlePlayPause(lesson.id)}
                          sx={{ 
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            backgroundColor: 'rgba(0,0,0,0.6)',
                            color: 'white',
                            '&:hover': { 
                              backgroundColor: 'rgba(0,0,0,0.8)' 
                            }
                          }}
                        >
                          {playingId === lesson.id ? <PauseIcon /> : <PlayArrowIcon />}
                        </IconButton>
                      </Box>
                      <Typography variant="body2" fontWeight="medium">
                        {lesson.title}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{lesson.duration}</TableCell>
                  <TableCell>{lesson.category}</TableCell>
                  <TableCell>{lesson.created}</TableCell>
                  <TableCell>{lesson.views.toLocaleString()}</TableCell>
                  <TableCell>
                    <Chip 
                      label={lesson.status} 
                      size="small"
                      sx={{ 
                        backgroundColor: `${getStatusColor(lesson.status)}20`,
                        color: getStatusColor(lesson.status),
                        fontWeight: 'medium',
                        textTransform: 'capitalize'
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex' }}>
                      <IconButton size="small" sx={{ color: '#1e88e5' }}>
                        <VisibilityIcon fontSize="small" />
                      </IconButton>
                      <IconButton size="small" sx={{ color: '#1e88e5' }}>
                        <EditIcon fontSize="small" />
                      </IconButton>
                      <IconButton size="small" sx={{ color: '#f44336' }}>
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        {/* Pagination */}
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <Pagination 
            count={10} 
            page={page} 
            onChange={handleChangePage} 
            color="primary"
            sx={{
              '& .MuiPaginationItem-root': {
                borderRadius: 2
              }
            }}
          />
        </Box>
      </Box>
    </Box>
  )
}