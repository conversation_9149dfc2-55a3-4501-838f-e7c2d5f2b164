services:
  # Development environment
  vite-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "4000:4000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    command: npm run dev -- --host

  # Production environment
  vite-prod:
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=production
